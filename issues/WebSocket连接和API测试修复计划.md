# StoryWeaver WebSocket连接和API测试修复 - 详细执行计划

**创建时间**: 2025年7月9日  
**计划版本**: v1.0  
**预计总时间**: 90分钟  
**优先级**: 🔴 高优先级  

---

## 📋 项目概述

### 目标
修复StoryWeaver项目的WebSocket连接问题，完成生产环境API测试验证，确保渐进式内容展示功能正常工作。

### 背景
根据项目进度报告分析，当前存在以下关键问题：
- WebSocket连接失败（错误代码1006）
- 缺少完整的生产环境API测试
- 渐进式内容展示功能需要验证

### 成功标准
- WebSocket连接成功率 > 90%
- 故事生成成功率 > 95%
- 实时进度更新正常工作
- 文本、图片、音频三阶段完整生成

---

## 🎯 阶段1：现状诊断和问题定位（预计15分钟）

### 1.1 WebSocket代码深度分析（5分钟）
**目标**: 使用ACE分析WebSocket连接相关代码，识别潜在问题

**涉及文件**:
- `frontend/src/services/durableObjects/storyGenerationClient.ts`
- `frontend/src/pages/CreateStoryPage.tsx` (startProgressMonitoring函数)
- `backend/src/index.ts` (WebSocket升级处理)

**分析重点**:
- WebSocket URL构建逻辑
- 环境变量VITE_API_BASE_URL的处理
- 连接错误处理机制
- Cloudflare Workers WebSocket支持限制

**预期结果**: 识别URL构建错误或连接逻辑问题

### 1.2 生产环境连接状态测试（8分钟）
**目标**: 使用Playwright测试当前WebSocket连接状态

**测试步骤**:
1. 导航到 https://storyweaver.pages.dev
2. 执行用户登录流程
3. 创建新故事并监控WebSocket连接
4. 记录连接错误详情和错误代码
5. 验证HTTP降级机制是否触发

**关键监控点**:
- WebSocket连接URL是否正确
- 连接建立是否成功
- 错误代码和错误消息
- HTTP轮询是否自动启动

**预期结果**: 获得详细的连接失败原因和错误日志

### 1.3 问题根因分析报告（2分钟）
**目标**: 基于代码分析和测试结果，确定修复方向

**分析内容**:
- WebSocket连接失败的具体原因
- 是否为URL构建问题
- 是否为Cloudflare Workers限制
- HTTP降级机制的工作状态

**输出**: 明确的问题根因和修复策略

---

## 🔧 阶段2：WebSocket连接直接修复（预计30分钟）

### 2.1 WebSocket URL构建修复（10分钟）
**目标**: 修复URL构建逻辑，确保正确的WebSocket连接地址

**修改文件**: `frontend/src/services/durableObjects/storyGenerationClient.ts`

**修复内容**:
```typescript
// 修复前可能的问题
const wsUrl = `wss://${apiBaseUrl}/ai-queue/${storyId}/websocket`;

// 修复后的正确逻辑
const host = apiBaseUrl.replace(/^https?:\/\//, '').replace(/\/api$/, '');
const wsUrl = `wss://${host}/ai-queue/${storyId}/websocket`;
```

**验证点**:
- 正确去除/api后缀
- 协议转换正确（https → wss）
- URL格式符合后端路由

### 2.2 连接错误处理优化（10分钟）
**目标**: 改进错误处理和重试机制

**修改文件**: `frontend/src/pages/CreateStoryPage.tsx`

**优化内容**:
- 添加详细的连接错误日志
- 实现连接重试机制（最多3次）
- 优化错误状态显示
- 改进用户提示信息

**代码行数范围**: 约50-100行修改

### 2.3 HTTP降级机制强化（8分钟）
**目标**: 确保WebSocket失败时HTTP轮询稳定工作

**修改内容**:
- 优化WebSocket失败检测逻辑
- 改进HTTP轮询切换机制
- 添加连接状态指示器
- 优化轮询频率和性能

**预期结果**: 用户在WebSocket失败时仍能看到进度更新

### 2.4 修复代码部署验证（2分钟）
**目标**: 将修复的代码部署到生产环境

**部署步骤**:
1. 构建前端代码：`cd frontend && npm run build`
2. 同步到生产目录：`rsync -av frontend/dist/ frontend-production/dist/`
3. 部署到Cloudflare Pages：`cd frontend-production && npx wrangler pages deploy dist`

**验证**: 确认部署成功，新版本生效

---

## ✅ 阶段3：完整功能验证测试（预计45分钟）

### 3.1 用户认证和登录测试（8分钟）
**目标**: 验证生产环境用户认证流程

**测试内容**:
- Google OAuth登录流程
- JWT令牌获取和验证
- 用户状态保持
- 认证错误处理

**使用工具**: Playwright自动化测试
**预期结果**: 用户可以正常登录并保持认证状态

### 3.2 故事创建和生成流程测试（20分钟）
**目标**: 测试完整的故事生成流程

**测试步骤**:
1. 创建新故事（输入主题和参数）
2. 监控WebSocket连接建立
3. 验证文本生成阶段（thinkingBudget修复验证）
4. 验证图片生成阶段（imagen模型修复验证）
5. 验证音频生成阶段（TTS功能验证）
6. 确认故事完成和数据保存

**关键验证点**:
- 每个阶段的成功率
- 生成内容的质量和完整性
- 错误处理和重试机制
- 总体生成时间（< 5分钟）

### 3.3 渐进式内容展示功能验证（10分钟）
**目标**: 验证ProgressiveStoryViewer组件的实时更新

**测试内容**:
- 实时进度指示器更新
- 内容完成时的立即显示
- 页面导航和状态管理
- /stories/:id/live路由功能
- 完成后自动跳转

**预期结果**: 用户可以实时看到故事生成过程

### 3.4 WebSocket实时通信验证（5分钟）
**目标**: 测试WebSocket连接稳定性和消息传递

**测试内容**:
- WebSocket连接建立成功
- 实时消息接收和处理
- 连接断开重连机制
- 消息类型和数据格式

**监控指标**:
- 连接成功率
- 消息延迟时间
- 重连成功率

### 3.5 数据完整性和存储验证（2分钟）
**目标**: 确认生成的故事数据完整且正确存储

**验证内容**:
- 故事数据结构完整性
- 文本内容质量和格式
- 图片资源存储到R2
- 音频文件存储和访问
- 数据库记录正确性

**预期结果**: 所有资源正确保存，用户可以正常访问

---

## 📊 质量检查和验收标准

### 技术指标
- [ ] WebSocket连接成功率 > 90%
- [ ] 故事文本生成成功率 > 95%
- [ ] 图片生成成功率 > 90%
- [ ] 音频生成成功率 > 90%
- [ ] API响应时间 < 30秒
- [ ] 完整故事生成时间 < 5分钟

### 功能指标
- [ ] 完整故事包含6-8页内容
- [ ] 每页包含文本、图片、音频
- [ ] 故事内容符合儿童友好标准
- [ ] 所有资源正确存储到R2
- [ ] 渐进式内容展示正常工作
- [ ] 用户体验流畅无阻塞

### 用户体验指标
- [ ] 页面加载时间 < 3秒
- [ ] 实时进度更新延迟 < 2秒
- [ ] 错误提示清晰友好
- [ ] 页面跳转流畅自然

---

## 🚨 风险评估和应对策略

### 高风险项
1. **Cloudflare Workers WebSocket限制**
   - 风险: WebSocket可能根本不支持
   - 应对: 强化HTTP轮询机制，确保功能可用

2. **AI生成服务不稳定**
   - 风险: Google AI API可能出现错误
   - 应对: 添加重试机制和错误处理

### 中风险项
1. **部署过程中的配置问题**
   - 风险: 环境变量或配置错误
   - 应对: 仔细验证部署配置

2. **测试环境数据污染**
   - 风险: 测试数据影响生产环境
   - 应对: 使用专门的测试账户

---

## 📈 成功后的后续计划

### 立即优化（1-2天）
- 性能监控设置
- 错误报告机制配置
- 用户反馈收集

### 中期改进（1周）
- WebSocket连接优化
- 缓存机制改进
- 用户体验细节优化

### 长期规划（1个月）
- 多语言支持完善
- 更多故事风格选项
- 系统性能优化

---

**计划制定人**: Augment Agent  
**审核状态**: 待用户确认  
**下次更新**: 执行完成后